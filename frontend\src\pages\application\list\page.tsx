import React, { useState, useEffect } from "react";
import AppLayout from "@/layouts/app-layout";
import { Card, CardContent } from "@/components/ui/card";
import { DataTable } from "@/components/data-table/DataTable";
import { type ColumnDef, type PaginationState } from "@tanstack/react-table";

interface JettyApplication {
  id: string;
  docnum: string;
  vesselName: string;
  applicationDate: string;
  status: "Pending" | "Approved" | "Rejected" | "Draft";
}

// Define the exact type expected by the DataTable component from data-table.tsx's schema
interface DataTableItem {
  id: number;
  header: string; // For display in DataTable's 'Header' column
  owner: string; // For DataTable's internal filter (expects 'owner')
  type: string;
  status: string;
  target: string;
  limit: string;
  reviewer: string;
}

const mockApplications: JettyApplication[] = [
  {
    id: "1",
    docnum: "APP-001",
    vesselName: "MV. Sea Princess",
    applicationDate: "2024-03-01",
    status: "Pending",
  },
  {
    id: "2",
    docnum: "APP-002",
    vesselName: "MV. Ocean Queen",
    applicationDate: "2024-03-05",
    status: "Approved",
  },
  {
    id: "3",
    docnum: "APP-003",
    vesselName: "MV. River King",
    applicationDate: "2024-03-10",
    status: "Rejected",
  },
  {
    id: "4",
    docnum: "APP-004",
    vesselName: "MV. Star Light",
    applicationDate: "2024-03-15",
    status: "Draft",
  },
  {
    id: "5",
    docnum: "APP-005",
    vesselName: "MV. Blue Sky",
    applicationDate: "2024-03-20",
    status: "Pending",
  },
];

// Transform JettyApplication to DataTableItem type for DataTable
const transformToDataTableItem = (apps: JettyApplication[]): DataTableItem[] => {
  return apps.map(app => ({
    id: parseInt(app.id),
    header: app.docnum, // Map docnum to header for display
    owner: app.docnum, // Map docnum to owner for internal filter
    type: app.vesselName,
    status: app.status,
    target: app.applicationDate,
    limit: "N/A",
    reviewer: "",
  }));
};

const columns: ColumnDef<DataTableItem>[] = [
  {
    accessorKey: "header",
    header: "Document Number",
  },
  {
    accessorKey: "type",
    header: "Vessel Name",
  },
  {
    accessorKey: "status",
    header: "Status",
  },
  {
    accessorKey: "target",
    header: "Application Date",
  },
  {
    accessorKey: "limit",
    header: "Limit",
  },
  {
    accessorKey: "reviewer",
    header: "Reviewer",
  },
];

const ApplicationListPage: React.FC = () => {
  const [data, setData] = useState<DataTableItem[]>([]);
  const [searchStr, setSearchStr] = useState<string>('');
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  useEffect(() => {
    // Filter mock data based on search string
    const filteredData = mockApplications.filter(app =>
      app.docnum.toLowerCase().includes(searchStr.toLowerCase()) ||
      app.vesselName.toLowerCase().includes(searchStr.toLowerCase())
    );
    setData(transformToDataTableItem(filteredData));
  }, [searchStr]); // Re-run effect when searchStr changes

  const handleSearch = (value: string) => {
    setSearchStr(value);
    setPagination(prev => ({ ...prev, pageIndex: 0 })); // Reset to first page on search
  };

  const handlePaginationChange = (newPagination: PaginationState) => {
    setPagination(newPagination);
  };

  return (
    <AppLayout>
      <div className="flex flex-col space-y-4 p-4">
        <Card>
          <CardContent>
            <DataTable
              title="Jetty Application List"
              columns={columns}
              data={data}
              totalCount={mockApplications.length} // Using total length of mock data
              isLoading={false}
              manualPagination={false} // Since we're using mock data, let DataTable handle pagination
              pageSize={pagination.pageSize}
              onPaginationChange={handlePaginationChange}
              onSearch={handleSearch}
              searchValue={searchStr}
              hideDefaultFilterbar={true} // Hide default filter bar, use a simple search for now
              enableRowSelection={false}
            />
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
};

export default ApplicationListPage;