using Imip.JettyApproval.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;
using Volo.Abp.MultiTenancy;

namespace Imip.JettyApproval.Permissions;

public class JettyApprovalPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var myGroup = context.AddGroup(JettyApprovalPermissions.GroupName);

        // Define your own permissions here. Example:
        //myGroup.AddPermission(JettyApprovalPermissions.MyPermission1, L("Permission:MyPermission1"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<JettyApprovalResource>(name);
    }
}
