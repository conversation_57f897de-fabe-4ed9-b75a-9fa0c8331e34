using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Services;
using Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Users;
using Imip.JettyApproval.Web.Services.Interfaces;

namespace Imip.JettyApproval.Application.ApplicationConfiguration;

/// <summary>
/// Custom ApplicationConfigurationAppService that fetches authorization data from the identity server
/// instead of the local database
/// </summary>
[Dependency(ReplaceServices = true)]
[ExposeServices(typeof(IApplicationConfigurationAppService))]
public class IdentityServerApplicationConfigurationAppService : ApplicationService, IApplicationConfigurationAppService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;
    private readonly ILogger<IdentityServerApplicationConfigurationAppService> _logger;
    private readonly ITokenService _tokenService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public IdentityServerApplicationConfigurationAppService(
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration,
        ILogger<IdentityServerApplicationConfigurationAppService> logger,
        ITokenService tokenService,
        IHttpContextAccessor httpContextAccessor)
    {
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
        _logger = logger;
        _tokenService = tokenService;
        _httpContextAccessor = httpContextAccessor;
    }

    [Authorize]
    public async Task<ApplicationConfigurationDto> GetAsync(ApplicationConfigurationRequestOptions options)
    {
        _logger.LogDebug("Getting application configuration from Identity Server");

        try
        {
            // Get the access token
            var accessToken = await _tokenService.GetAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
            {
                _logger.LogWarning("No access token found, returning default configuration");
                return await GetDefaultConfigurationAsync(options);
            }

            // Fetch configuration from Identity Server
            var identityServerConfig = await GetConfigurationFromIdentityServerAsync(accessToken, options);
            if (identityServerConfig != null)
            {
                _logger.LogDebug("Successfully retrieved configuration from Identity Server");
                return identityServerConfig;
            }

            _logger.LogWarning("Failed to get configuration from Identity Server, falling back to default");
            return await GetDefaultConfigurationAsync(options);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting application configuration from Identity Server");
            return await GetDefaultConfigurationAsync(options);
        }
    }

    private async Task<ApplicationConfigurationDto?> GetConfigurationFromIdentityServerAsync(
        string accessToken, 
        ApplicationConfigurationRequestOptions options)
    {
        try
        {
            var identityServerUrl = _configuration["AuthServer:Authority"];
            var endpoint = $"{identityServerUrl}/api/abp/application-configuration";

            // Build query parameters
            var queryParams = new List<string>();
            if (options.IncludeLocalizationResources.HasValue)
            {
                queryParams.Add($"IncludeLocalizationResources={options.IncludeLocalizationResources.Value}");
            }

            if (queryParams.Count > 0)
            {
                endpoint += "?" + string.Join("&", queryParams);
            }

            _logger.LogDebug("Fetching configuration from: {Endpoint}", endpoint);

            var client = _httpClientFactory.CreateClient("IdentityServer");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
            client.Timeout = TimeSpan.FromSeconds(30);

            // Add fallback DNS resolution for known hosts
            var response = await TryGetWithFallbackAsync(client, endpoint, identityServerUrl);

            if (response?.IsSuccessStatusCode == true)
            {
                var content = await response.Content.ReadAsStringAsync();
                var config = JsonSerializer.Deserialize<ApplicationConfigurationDto>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (config != null)
                {
                    _logger.LogDebug("Successfully parsed configuration from Identity Server");
                    _logger.LogDebug("Granted policies count: {Count}", 
                        config.Auth?.GrantedPolicies?.Count ?? 0);
                    _logger.LogDebug("Current user: {UserName}", 
                        config.CurrentUser?.UserName ?? "Unknown");
                    _logger.LogDebug("User roles count: {Count}", 
                        config.CurrentUser?.Roles?.Length ?? 0);

                    return config;
                }
            }
            else
            {
                _logger.LogWarning("Failed to get configuration from Identity Server. Status: {Status}", 
                    response?.StatusCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching configuration from Identity Server");
        }

        return null;
    }

    private async Task<HttpResponseMessage?> TryGetWithFallbackAsync(HttpClient client, string endpoint, string identityServerUrl)
    {
        // Add fallback DNS resolution for known hosts
        var knownHosts = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "api-identity-dev.imip.co.id", "**********" },
            { "identity.imip.co.id", "**********" }
        };

        // Try with fallback IP first if hostname is known
        if (!string.IsNullOrEmpty(identityServerUrl))
        {
            try
            {
                var uri = new Uri(identityServerUrl);
                var hostname = uri.Host;

                if (knownHosts.TryGetValue(hostname, out var fallbackIp))
                {
                    var scheme = uri.Scheme;
                    var port = uri.Port > 0 && uri.Port != 80 && uri.Port != 443 ? $":{uri.Port}" : "";
                    var path = uri.AbsolutePath.TrimEnd('/');
                    
                    var fallbackEndpoint = endpoint.Replace(hostname, fallbackIp);
                    
                    _logger.LogDebug("Trying fallback endpoint: {FallbackEndpoint}", fallbackEndpoint);
                    
                    var fallbackResponse = await client.GetAsync(fallbackEndpoint);
                    if (fallbackResponse.IsSuccessStatusCode)
                    {
                        _logger.LogDebug("Successfully used fallback endpoint");
                        return fallbackResponse;
                    }
                    
                    _logger.LogDebug("Fallback failed, trying original endpoint");
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Error with fallback, trying original endpoint");
            }
        }

        // Try with original endpoint
        return await client.GetAsync(endpoint);
    }

    private async Task<ApplicationConfigurationDto> GetDefaultConfigurationAsync(ApplicationConfigurationRequestOptions options)
    {
        // Create a minimal configuration with current user information from claims
        var config = new ApplicationConfigurationDto
        {
            Auth = new ApplicationAuthConfigurationDto
            {
                GrantedPolicies = new Dictionary<string, bool>()
            },
            CurrentUser = GetCurrentUserFromClaims()
        };

        _logger.LogDebug("Returning default configuration with {PolicyCount} policies", 
            config.Auth.GrantedPolicies.Count);

        return config;
    }

    private CurrentUserDto? GetCurrentUserFromClaims()
    {
        var user = CurrentUser;
        if (user?.IsAuthenticated != true)
        {
            return null;
        }

        return new CurrentUserDto
        {
            IsAuthenticated = true,
            Id = user.Id,
            UserName = user.UserName,
            Name = user.Name,
            SurName = user.SurName,
            Email = user.Email,
            Roles = user.Roles?.ToArray() ?? Array.Empty<string>()
        };
    }
}
