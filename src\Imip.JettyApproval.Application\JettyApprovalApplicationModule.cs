using Volo.Abp.PermissionManagement;
using Volo.Abp.SettingManagement;
using Volo.Abp.Account;
using Volo.Abp.Identity;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Modularity;
using Volo.Abp.TenantManagement;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.AutoMapper;
using System.Linq;
using Imip.JettyApproval.Mapping;
using Imip.JettyApproval.Mapping.Mappers;

namespace Imip.JettyApproval;

[DependsOn(
    typeof(JettyApprovalDomainModule),
    typeof(JettyApprovalApplicationContractsModule),
    typeof(AbpPermissionManagementApplicationModule),
    typeof(AbpFeatureManagementApplicationModule),
    typeof(AbpIdentityApplicationModule),
    typeof(AbpAccountApplicationModule),
    typeof(AbpTenantManagementApplicationModule),
    typeof(AbpSettingManagementApplicationModule)
    )]
public class JettyApprovalApplicationModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var services = context.Services;
        context.Services.AddAutoMapperObjectMapper<JettyApprovalApplicationModule>();
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<JettyApprovalApplicationModule>(validate: true);
        });

        // Auto-register Mapperly mapper classes
        ConfigureMapperlyMappers(services);

        // Manually register application services that are not auto-registered
        ConfigureApplicationServices(services);
    }

    private void ConfigureApplicationServices(IServiceCollection services)
    {
        // Register BoundedZoneAppService manually since it's in a different namespace
        // services.AddTransient<IZoneDetailAppService, BoundedZoneAppService>();
    }

    private void ConfigureMapperlyMappers(IServiceCollection services)
    {
        // Register all Mapperly mapper classes that have the [Mapper] attribute
        var mapperTypes = typeof(JettyApprovalApplicationModule).Assembly
             .GetTypes()
             .Where(t => typeof(IMapperlyMapper).IsAssignableFrom(t) && t.IsClass && !t.IsAbstract)
             .ToArray();

        foreach (var mapperType in mapperTypes)
        {
            // Skip BaseEntityMapper as it's abstract and used as base class
            if (mapperType == typeof(BaseEntityMapper))
                continue;

            services.AddTransient(mapperType);
        }
    }
}
