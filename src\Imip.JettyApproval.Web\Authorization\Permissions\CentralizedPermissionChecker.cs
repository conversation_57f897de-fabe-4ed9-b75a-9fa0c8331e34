using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using Imip.JettyApproval.Web.Services.Interfaces;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.DependencyInjection;

namespace Imip.JettyApproval.Web.Authorization.Permissions;

[Dependency(ReplaceServices = true)]
[ExposeServices(typeof(IPermissionChecker))]
public class CentralizedPermissionChecker : IPermissionChecker, ITransientDependency
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<CentralizedPermissionChecker> _logger;
    private readonly IWebHostEnvironment _environment;
    private readonly ApplicationConfigurationService _applicationConfigurationService;

    private readonly ITokenService _tokenService;

    public CentralizedPermissionChecker(
        IHttpContextAccessor httpContextAccessor,
        ILogger<CentralizedPermissionChecker> logger,
        IWebHostEnvironment environment,
        ApplicationConfigurationService applicationConfigurationService,
        ITokenService tokenService)
    {
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
        _environment = environment;
        _applicationConfigurationService = applicationConfigurationService;
        _tokenService = tokenService;
    }

    // Implementation for IsGrantedAsync(string name)
    public async Task<bool> IsGrantedAsync(string name)
    {
        return await IsGrantedAsync(_httpContextAccessor.HttpContext?.User, name);
    }

    // Implementation for IsGrantedAsync(ClaimsPrincipal? principal, string name)
    public async Task<bool> IsGrantedAsync(ClaimsPrincipal? principal, string name)
    {
        try
        {
            // Special case: Allow access to Quartz endpoints for authenticated users
            // var httpContext = _httpContextAccessor.HttpContext;
            // if (httpContext != null && httpContext.Request.Path.StartsWithSegments("/quartz"))
            // {
            //     // _logger.LogDebug("Allowing access to Quartz endpoint: {Path} for authenticated user", httpContext.Request.Path);
            //     return true;
            // }

            if (principal == null)
            {
                _logger.LogWarning("Principal is null when checking permission: {Permission}", name);
                return false;
            }

            if (!principal.Identity?.IsAuthenticated ?? true)
            {
                _logger.LogDebug("User is not authenticated when checking permission: {Permission}", name);
                return false;
            }

            // Log the permission being checked
            _logger.LogDebug("Checking permission: {Permission} for user: {UserId}", name,
                principal.FindFirstValue(ClaimTypes.NameIdentifier) ?? "unknown");

            // Get the access token
            var identityServerToken = await GetAccessTokenAsync();
            if (string.IsNullOrEmpty(identityServerToken))
            {
                _logger.LogWarning("Access token is null or empty when checking permission: {Permission}", name);
                return false;
            }

            // Log token information (only length for security)
            _logger.LogDebug("Using token for permission check: {TokenLength} characters",
                identityServerToken.Length);

            // Get granted policies from identity server's application configuration endpoint
            var grantedPolicies = await _applicationConfigurationService.GetGrantedPoliciesAsync(identityServerToken);

            // Log granted policies count for debugging
            _logger.LogDebug("Retrieved {Count} granted policies from Identity Server application configuration",
                grantedPolicies.Count);

            // If we have no granted policies, log this as it might indicate a configuration issue
            if (grantedPolicies.Count == 0)
            {
                _logger.LogWarning("No granted policies found in Identity Server application configuration for permission check: {Permission}", name);
                return false;
            }

            // Try with the original permission name
            if (grantedPolicies.TryGetValue(name, out var isGranted) && isGranted)
            {
                _logger.LogDebug("Permission {Permission} is granted in application configuration", name);
                return true;
            }

            // Try with the "IdentityServer." prefix if the permission doesn't have it
            if (!name.StartsWith("IdentityServer."))
            {
                var identityServerPermissionName = "IdentityServer." + name;
                if (grantedPolicies.TryGetValue(identityServerPermissionName, out isGranted) && isGranted)
                {
                    _logger.LogDebug(
                        "Permission {Permission} is granted in application configuration as {IdentityServerPermissionName}",
                        name, identityServerPermissionName);
                    return true;
                }
            }

            // Check for parent permission (e.g., if WismaApp.Room.View is not found, check WismaApp.Room)
            var lastDotIndex = name.LastIndexOf('.');
            if (lastDotIndex > 0)
            {
                var parentPermission = name[..lastDotIndex]; // Using range operator instead of Substring
                if (grantedPolicies.TryGetValue(parentPermission, out isGranted) && isGranted)
                {
                    _logger.LogDebug("Parent permission {ParentPermission} is granted for {Permission}",
                        parentPermission, name);
                    return true;
                }
            }

            // Permission not found in granted policies - deny access
            _logger.LogDebug("Permission {Permission} not found in Identity Server application configuration granted policies", name);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission {Permission}", name);
            return false;
        }
    }

    // Implementation for IsGrantedAsync(Guid userId, string name)
    public async Task<bool> IsGrantedAsync(Guid userId, string name)
    {
        try
        {
            // Log permission check with appropriate level based on environment
            if (_environment.IsDevelopment())
            {
                _logger.LogDebug("Checking permission for user {UserId}: {Permission}", userId, name);
            }

            // Get the access token
            var accessToken = await GetAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
            {
                _logger.LogWarning(
                    "Access token is null or empty when checking permission for user: {UserId}, {Permission}", userId,
                    name);
                return false;
            }

            // Get granted policies from identity server's application configuration endpoint
            var grantedPolicies = await _applicationConfigurationService.GetGrantedPoliciesAsync(accessToken);

            // Check if the permission is granted
            if (grantedPolicies.TryGetValue(name, out var isGranted) && isGranted)
            {
                _logger.LogDebug("Permission {Permission} is granted for user {UserId}", name, userId);
                return true;
            }

            _logger.LogDebug("Permission {Permission} not found in granted policies for user {UserId}", name, userId);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission for user ID {UserId}, {Permission}", userId, name);
            return false;
        }
    }

    // Implementation for IsGrantedAsync(string[] names)
    public async Task<MultiplePermissionGrantResult> IsGrantedAsync(string[] names)
    {
        return await IsGrantedAsync(_httpContextAccessor.HttpContext?.User, names);
    }

    // Implementation for IsGrantedAsync(ClaimsPrincipal? principal, string[] names)
    public async Task<MultiplePermissionGrantResult> IsGrantedAsync(ClaimsPrincipal? principal, string[] names)
    {
        var result = new MultiplePermissionGrantResult();

        if (names == null || names.Length == 0)
        {
            return result;
        }

        try
        {
            // Log with appropriate level based on environment
            if (_environment.IsDevelopment())
            {
                _logger.LogDebug("Checking permissions: {Permissions}", string.Join(", ", names));
            }

            // If we have a principal, try to check permissions directly
            if (principal != null && principal.Identity?.IsAuthenticated == true)
            {
                // Get the access token
                var accessToken = await GetAccessTokenAsync();
                if (!string.IsNullOrEmpty(accessToken))
                {
                    // Get granted policies from identity server's application configuration endpoint
                    var grantedPolicies = await _applicationConfigurationService.GetGrantedPoliciesAsync(accessToken);

                    _logger.LogDebug("Checking multiple permissions against {Count} granted policies", grantedPolicies.Count);

                    // Check each permission against the granted policies
                    foreach (var name in names)
                    {
                        var grantResult = PermissionGrantResult.Prohibited;

                        // Check if permission is granted
                        if (grantedPolicies.TryGetValue(name, out var isGranted) && isGranted)
                        {
                            grantResult = PermissionGrantResult.Granted;
                        }
                        // Try with "IdentityServer." prefix
                        else if (!name.StartsWith("IdentityServer."))
                        {
                            var identityServerPermissionName = "IdentityServer." + name;
                            if (grantedPolicies.TryGetValue(identityServerPermissionName, out isGranted) && isGranted)
                            {
                                grantResult = PermissionGrantResult.Granted;
                            }
                        }

                        result.Result[name] = grantResult;
                    }

                    return result;
                }
            }

            // Fallback: check permissions one by one using the same application configuration approach
            foreach (var name in names)
            {
                result.Result[name] = await IsGrantedAsync(principal, name)
                    ? PermissionGrantResult.Granted
                    : PermissionGrantResult.Prohibited;
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking multiple permissions");

            // In case of error, mark all permissions as prohibited
            foreach (var name in names)
            {
                result.Result[name] = PermissionGrantResult.Prohibited;
            }

            return result;
        }
    }

    /// <summary>
    /// Gets all granted policies from Identity Server for the given access token
    /// </summary>
    public async Task<Dictionary<string, bool>> GetAllGrantedPoliciesAsync(string accessToken)
    {
        try
        {
            _logger.LogDebug("Getting all granted policies from Identity Server");

            // Use the ApplicationConfigurationService which has better error handling and fallback logic
            var grantedPolicies = await _applicationConfigurationService.GetGrantedPoliciesAsync(accessToken);

            _logger.LogDebug("Successfully fetched {Count} granted policies from Identity Server", grantedPolicies.Count);

            // Log all fetched policies for debugging
            foreach (var policy in grantedPolicies)
            {
                _logger.LogDebug("Policy: {PolicyName} = {IsGranted}", policy.Key, policy.Value);
            }

            return grantedPolicies;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all granted policies from Identity Server");

            // Return empty dictionary as fallback
            return new Dictionary<string, bool>();
        }
    }

    private async Task<string?> GetAccessTokenAsync()
    {
        return await _tokenService.GetAccessTokenAsync();
    }


}