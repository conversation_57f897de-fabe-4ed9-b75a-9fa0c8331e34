using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Imip.JettyApproval.Web.Authorization.Permissions;
using Imip.JettyApproval.Web.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Volo.Abp.Authorization.Permissions;

namespace Imip.JettyApproval.Web.Controllers;

[Route("api/test")]
[ApiController]
[Authorize]
public class TestApplicationConfigurationController : ControllerBase
{
    private readonly IPermissionChecker _permissionChecker;
    private readonly ITokenService _tokenService;
    private readonly ILogger<TestApplicationConfigurationController> _logger;

    public TestApplicationConfigurationController(
        IPermissionChecker permissionChecker,
        ITokenService tokenService,
        ILogger<TestApplicationConfigurationController> logger)
    {
        _permissionChecker = permissionChecker;
        _tokenService = tokenService;
        _logger = logger;
    }

    [HttpGet("granted-policies")]
    public async Task<IActionResult> GetGrantedPolicies()
    {
        try
        {
            var accessToken = await _tokenService.GetAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
            {
                return Unauthorized("No access token found");
            }

            // Cast to CentralizedPermissionChecker to access the custom method
            if (_permissionChecker is CentralizedPermissionChecker centralizedPermissionChecker)
            {
                var grantedPolicies = await centralizedPermissionChecker.GetAllGrantedPoliciesAsync(accessToken);

                _logger.LogInformation("Test endpoint: Retrieved {Count} granted policies from Identity Server", grantedPolicies.Count);

                return Ok(new
                {
                    Count = grantedPolicies.Count,
                    Policies = grantedPolicies
                });
            }
            else
            {
                _logger.LogWarning("Permission checker is not CentralizedPermissionChecker");
                return StatusCode(500, "Permission checker is not the expected type");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting granted policies in test endpoint");
            return StatusCode(500, "Internal server error");
        }
    }
}