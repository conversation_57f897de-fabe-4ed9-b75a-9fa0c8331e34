using Imip.JettyApproval.EntityFrameworkCore;
using Imip.JettyApproval.Localization;
using Imip.JettyApproval.MultiTenancy;
using Imip.JettyApproval.Web.Menus;
using Imip.JettyApproval.Web.Middleware;
using Imip.JettyApproval.Web.Modules;
using Imip.JettyApproval.Web.Services.Interfaces;
using Imip.JettyApproval.Web.Services;
using InertiaCore.Extensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using System.IO;
using Volo.Abp;
using Volo.Abp.Account.Web;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations;
using Volo.Abp.AspNetCore.Mvc.Localization;
using Volo.Abp.AspNetCore.Mvc.UI.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.Toolbars;
using Volo.Abp.AspNetCore.Serilog;
using Volo.Abp.Autofac;
using Volo.Abp.AutoMapper;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Identity.Web;
using Volo.Abp.Modularity;
using Volo.Abp.PermissionManagement;
using Volo.Abp.Studio.Client.AspNetCore;
using Volo.Abp.Swashbuckle;
using Volo.Abp.TenantManagement.Web;
using Volo.Abp.UI.Navigation;
using Volo.Abp.UI.Navigation.Urls;
using Volo.Abp.VirtualFileSystem;
using Volo.Abp.Caching.StackExchangeRedis;
using Volo.Abp.BackgroundJobs.Quartz;
using Volo.Abp.BackgroundWorkers.Quartz;
using Volo.Abp.Quartz;
using Imip.JettyApproval.SilkierQuartz;
using Quartz.Util;
using Quartz;
using Imip.JettyApproval.Web.Extensions;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Volo.Abp.Security.Claims;
using Imip.JettyApproval.Web.Authorization.Claims;
using Microsoft.AspNetCore.Authorization;
using Imip.JettyApproval.Web.Authorization.Permissions;
using OpenIddict.Server.AspNetCore;
using Microsoft.Extensions.Logging;
using Volo.Abp.Users;

namespace Imip.JettyApproval.Web;

[DependsOn(
    typeof(JettyApprovalHttpApiModule),
    typeof(JettyApprovalApplicationModule),
    typeof(JettyApprovalEntityFrameworkCoreModule),
    typeof(AbpAspNetCoreMvcModule),
    typeof(AbpAutofacModule),
    typeof(AbpStudioClientAspNetCoreModule),
    typeof(AbpIdentityWebModule),
    typeof(AbpAspNetCoreMvcUiLeptonXLiteThemeModule),
    typeof(AbpAccountWebModule),
    typeof(AbpTenantManagementWebModule),
    typeof(AbpFeatureManagementWebModule),
    typeof(AbpSwashbuckleModule),
    typeof(AbpAspNetCoreSerilogModule),
    typeof(PermissionCheckerModule),
    typeof(AbpCachingStackExchangeRedisModule),
    typeof(AbpBackgroundJobsQuartzModule),
    typeof(AbpBackgroundWorkersQuartzModule)
)]
public class JettyApprovalWebModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();

        // Configure OpenIddict validation
        PreConfigure<OpenIddictBuilder>(builder =>
        {
            builder.AddValidation(options =>
            {
                options.AddAudiences("IdentityServer");
                options.UseAspNetCore();
                options.UseLocalServer();
                options.SetIssuer(configuration["AuthServer:Authority"]!);
            });
        });

        context.Services.PreConfigure<AbpMvcDataAnnotationsLocalizationOptions>(options =>
        {
            options.AddAssemblyResource(
                typeof(JettyApprovalResource),
                typeof(JettyApprovalDomainModule).Assembly,
                typeof(JettyApprovalDomainSharedModule).Assembly,
                typeof(JettyApprovalApplicationModule).Assembly,
                typeof(JettyApprovalApplicationContractsModule).Assembly,
                typeof(JettyApprovalWebModule).Assembly
            );
        });

        PreConfigure<AbpQuartzOptions>(options =>
        {
            options.Configurator = configure =>
            {
                configure.SetProperty("quartz.plugin.recentHistory.type", typeof(AbpExecutionHistoryPlugin).AssemblyQualifiedNameWithoutVersion());
                configure.SetProperty("quartz.plugin.recentHistory.storeType", typeof(AbpExecutionHistoryStore).AssemblyQualifiedNameWithoutVersion());
                configure.UsePersistentStore(storeOptions =>
                {
                    storeOptions.UseProperties = true;
                    storeOptions.UseNewtonsoftJsonSerializer();
                    storeOptions.UseSqlServer(configuration.GetConnectionString("Default")!);
                    storeOptions.UseClustering(c =>
                    {
                        c.CheckinMisfireThreshold = TimeSpan.FromSeconds(20);
                        c.CheckinInterval = TimeSpan.FromSeconds(10);
                    });
                });

                // Configure schema for Quartz tables
                configure.SetProperty("quartz.jobStore.tablePrefix", "Quartz.QRTZ_");
                configure.SetProperty("quartz.jobStore.driverDelegateType", "Quartz.Impl.AdoJobStore.SqlServerDelegate, Quartz");

                // Disable schema validation to prevent startup failures if tables don't exist yet
                // This provides a safety net in case the migration hasn't completed
                configure.SetProperty("quartz.jobStore.performSchemaValidation", "false");
            };
        });
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();
        var appName = configuration["App:AppName"] ?? "Imip.JettyApproval";

        if (!configuration.GetValue<bool>("App:DisablePII"))
        {
            Microsoft.IdentityModel.Logging.IdentityModelEventSource.ShowPII = true;
            Microsoft.IdentityModel.Logging.IdentityModelEventSource.LogCompleteSecurityArtifact = true;
        }

        DataProtectionConfigurationService.ConfigureDataProtection(context.Services, configuration, hostingEnvironment);
        DistributedCacheConfigurationService.ConfigureDistributedCache(context.Services, configuration);

        // Register the authorization handler
        context.Services.AddScoped<IAuthorizationHandler, DynamicPolicyAuthorizationHandler>();

        // Register the ApplicationConfigurationService
        context.Services.AddScoped<ApplicationConfigurationService>();

        // Register the custom ApplicationConfigurationContributor using ABP configuration pattern
        Configure<AbpApplicationConfigurationOptions>(options =>
        {
            var serviceProvider = context.Services.BuildServiceProvider();
            options.Contributors.Add(new IdentityServerApplicationConfigurationContributor(
                serviceProvider.GetRequiredService<ILogger<IdentityServerApplicationConfigurationContributor>>(),
                serviceProvider.GetRequiredService<ITokenService>(),
                serviceProvider.GetRequiredService<ApplicationConfigurationService>(),
                serviceProvider.GetRequiredService<ICurrentUser>()
            ));
        });

        // Log the registration for debugging
        Console.WriteLine("=== IdentityServerApplicationConfigurationContributor registered via AbpApplicationConfigurationOptions ===");

        // For Kestrel
        context.Services.Configure<KestrelServerOptions>(options =>
        {
            options.Limits.MaxRequestHeadersTotalSize = 65536; // Increased header size limit (64KB)
        });

        if (!configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata"))
        {
            // Remove OpenIddict server options since we're not using OpenIddict as a server
            Configure<OpenIddictServerAspNetCoreOptions>(options =>
            {
                options.DisableTransportSecurityRequirement = true;
            });

            Configure<ForwardedHeadersOptions>(options =>
            {
                options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto | ForwardedHeaders.XForwardedHost;
                options.RequireHeaderSymmetry = false;
                options.ForwardedProtoHeaderName = "X-Forwarded-Proto";
                options.ForwardedHostHeaderName = "X-Forwarded-Host";

                // Trust the nginx proxy
                options.KnownNetworks.Clear();
                options.KnownProxies.Clear();
                options.KnownProxies.Add(System.Net.IPAddress.Any);
            });
        }

        AntiforgeryConfigurationService.ConfigureAntiforgery(context.Services, configuration, hostingEnvironment);

        context.Services.AddInertia(options =>
        {
            options.RootView = "~/Views/App.cshtml";
        });

        context.Services.AddViteHelper(options =>
        {
            options.PublicDirectory = "wwwroot";
            options.ManifestFilename = "manifest.json";
            options.BuildDirectory = "build";
        });

        ConfigureBundles();
        ConfigureUrls(configuration);
        ConfigureHealthChecks(context);
        // Register the HttpContextAccessor if it's not already registered
        context.Services.AddHttpContextAccessor();

        // Register HttpClientFactory for testing and other HTTP operations
        context.Services.AddHttpClient();

        // Register token service
        context.Services.AddScoped<ITokenService, TokenService>();
        context.Services.AddScoped<IAuthenticationTokenValidationService, AuthenticationTokenValidationService>();
        context.Services.AddScoped<ExternalApiService>();
        context.Services.AddScoped<AppToAppService>();
        // Register background token refresh service
        context.Services.AddHostedService<BackgroundTokenRefreshService>();

        AuthenticationConfigurationService.ConfigureAuthentication(context.Services, configuration);
        ConfigureAutoMapper();
        ConfigureVirtualFileSystem(hostingEnvironment);
        ConfigureNavigationServices();
        ConfigureAutoApiControllers();
        SwaggerConfigurationService.ConfigureSwagger(context.Services);

        // Configure authorization policies
        ConfigureAuthorizationPolicies(context.Services);

        Configure<PermissionManagementOptions>(options =>
        {
            options.IsDynamicPermissionStoreEnabled = true;
        });

        // Replace the default claims principal factory with our custom one
        context.Services.Replace(ServiceDescriptor.Transient<AbpClaimsPrincipalFactory, IdentityServerClaimsPrincipalFactory>());

        // Configure ABP to use our custom services for application configuration
        Configure<AbpAspNetCoreMvcOptions>(options =>
        {
            // Ensure our custom services are properly registered
            options.ConventionalControllers.Create(typeof(JettyApprovalWebModule).Assembly);
        });

        // Custom application configuration controller is registered automatically via conventional controllers

        // Configure session for deferred user synchronization
        context.Services.AddSession(options =>
        {
            options.IdleTimeout = TimeSpan.FromMinutes(30);
            options.Cookie.HttpOnly = true;
            options.Cookie.IsEssential = true;
            options.Cookie.Name = ".Imip.JettyApproval.Session";
        });
    }

    private void ConfigureHealthChecks(ServiceConfigurationContext context)
    {
        context.Services.AddHealthChecks();
    }

    private void ConfigureBundles()
    {
        Configure<AbpBundlingOptions>(options =>
        {
            options.StyleBundles.Configure(
                LeptonXLiteThemeBundles.Styles.Global,
                bundle =>
                {
                    bundle.AddFiles("/global-styles.css");
                }
            );

            options.ScriptBundles.Configure(
                LeptonXLiteThemeBundles.Scripts.Global,
                bundle =>
                {
                    bundle.AddFiles("/global-scripts.js");
                }
            );
        });
    }

    private void ConfigureUrls(IConfiguration configuration)
    {
        Configure<AppUrlOptions>(options =>
        {
            options.Applications["MVC"].RootUrl = configuration["App:SelfUrl"];
        });
    }



    private void ConfigureAutoMapper()
    {
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<JettyApprovalWebModule>();
        });
    }

    private static bool IsRunningInContainer()
    {
        // Check for container-specific environment indicators
        return File.Exists("/.dockerenv") ||
               (File.Exists("/proc/1/cgroup") && File.ReadAllText("/proc/1/cgroup").Contains("/docker/"));
    }

    private void ConfigureVirtualFileSystem(IWebHostEnvironment hostingEnvironment)
    {
        Configure<AbpVirtualFileSystemOptions>(options =>
        {
            options.FileSets.AddEmbedded<JettyApprovalWebModule>();

            // Only replace embedded resources with physical files in a development environment,
            // and only if we're not running in a container
            if (hostingEnvironment.IsDevelopment() && !IsRunningInContainer())
            {
                try
                {
                    var domainSharedPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.JettyApproval.Domain.Shared", Path.DirectorySeparatorChar));
                    if (Directory.Exists(domainSharedPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalDomainSharedModule>(domainSharedPath);
                    }

                    var domainPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.JettyApproval.Domain", Path.DirectorySeparatorChar));
                    if (Directory.Exists(domainPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalDomainModule>(domainPath);
                    }

                    var contractsPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.JettyApproval.Application.Contracts", Path.DirectorySeparatorChar));
                    if (Directory.Exists(contractsPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalApplicationContractsModule>(
                            contractsPath);
                    }

                    var appPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.JettyApproval.Application", Path.DirectorySeparatorChar));
                    if (Directory.Exists(appPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalApplicationModule>(appPath);
                    }

                    var httpApiPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}..{0}src{0}Imip.JettyApproval.HttpApi", Path.DirectorySeparatorChar));
                    if (Directory.Exists(httpApiPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalHttpApiModule>(httpApiPath);
                    }

                    if (Directory.Exists(hostingEnvironment.ContentRootPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalWebModule>(hostingEnvironment
                            .ContentRootPath);
                    }
                }
                catch (Exception ex)
                {
                    // Log the exception but continue without replacing embedded resources
                    Console.WriteLine($"Error configuring virtual file system: {ex.Message}");
                }
            }
        });
    }

    private void ConfigureNavigationServices()
    {
        Configure<AbpNavigationOptions>(options =>
        {
            options.MenuContributors.Add(new JettyApprovalMenuContributor());
        });

        Configure<AbpToolbarOptions>(options =>
        {
            options.Contributors.Add(new JettyApprovalToolbarContributor());
        });
    }

    private void ConfigureAutoApiControllers()
    {
        Configure<AbpAspNetCoreMvcOptions>(options =>
        {
            options.ConventionalControllers.Create(typeof(JettyApprovalApplicationModule).Assembly, opts =>
            {
                opts.RootPath = "idjas";
            });
        });
    }

    private void ConfigureAuthorizationPolicies(IServiceCollection services)
    {
        services.AddAuthorization(options =>
        {
            // Add a specific policy for Quartz endpoints
            options.AddPolicy("QuartzAccess", policy =>
            {
                policy.RequireAuthenticatedUser();
                // You can add additional requirements here if needed
                // For example, require specific roles or permissions
                // policy.RequireRole("Admin");
                // policy.RequirePermission("SilkierQuartz");
            });

            options.AddPolicy("SilkierQuartz", policy =>
            {
                policy.RequireAuthenticatedUser();
            });
        });
    }

    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        var app = context.GetApplicationBuilder();
        var env = context.GetEnvironment();

        app.UseForwardedHeaders();

        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        app.UseAbpRequestLocalization();

        if (!env.IsDevelopment())
        {
            app.UseErrorPage();
            app.UseHsts();
        }

        app.UseAbpSilkierQuartz();

        app.UseInertia();

        app.UseCorrelationId();
        app.MapAbpStaticAssets();
        app.UseAbpStudioLink();
        app.UseRouting();
        app.UseAbpSecurityHeaders();
        // app.UseSession(); // Required for deferred user synchronization
        app.UseAuthentication();

        // Add token refresh middleware
        app.UseMiddleware<TokenRefreshMiddleware>();

        app.UseAbpOpenIddictValidation();

        // Add user synchronization middleware after authentication
        // app.UseMiddleware<UserSynchronizationMiddleware>();

        if (MultiTenancyConsts.IsEnabled)
        {
            app.UseMultiTenancy();
        }

        app.UseUnitOfWork();
        app.UseDynamicClaims();
        app.UseAuthorization();
        app.UseSwagger();
        app.UseAbpSwaggerUI(options =>
        {
            options.SwaggerEndpoint("/swagger/v1/swagger.json", "JettyApproval API");
        });
        app.UseAuditing();
        app.UseAbpSerilogEnrichers();
        app.UseConfiguredEndpoints(endpoints =>
        {
            endpoints.MapAbpSilkierQuartz();
            endpoints.MapControllerRoute(
                name: "default",
                pattern: "{controller=Admin}/{action=Index}/{id?}");
        });
    }
}
