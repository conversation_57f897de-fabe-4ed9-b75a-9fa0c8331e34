using Imip.JettyApproval.Web.Services.Interfaces;
using Microsoft.Extensions.Logging;
using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;

namespace Imip.JettyApproval.Web.Services;

public class ExternalApiService
{
    private readonly ITokenService _tokenService;
    private readonly HttpClient _httpClient;
    private readonly ILogger<ExternalApiService> _logger;

    public ExternalApiService(ITokenService tokenService, HttpClient httpClient, ILogger<ExternalApiService> logger)
    {
        _tokenService = tokenService;
        _httpClient = httpClient;
        _logger = logger;
    }

    /// <summary>
    /// Calls an external API with automatic token refresh
    /// </summary>
    public async Task<string> CallExternalApiAsync(string apiUrl)
    {
        try
        {
            // Get a valid token (automatically refreshes if expired)
            var accessToken = await _tokenService.GetValidAccessTokenAsync();

            if (string.IsNullOrEmpty(accessToken))
            {
                _logger.LogError("No valid access token available");
                throw new UnauthorizedAccessException("No valid access token available");
            }

            // Set the authorization header
            _httpClient.DefaultRequestHeaders.Authorization =
                new AuthenticationHeaderValue("Bearer", accessToken);

            // Make the API call
            var response = await _httpClient.GetAsync(apiUrl);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("External API call successful");
                return content;
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
            {
                _logger.LogWarning("API returned 401, token might be invalid");

                // Try to refresh the token and retry once
                var newToken = await _tokenService.RefreshAccessTokenAsync();
                if (!string.IsNullOrEmpty(newToken))
                {
                    _httpClient.DefaultRequestHeaders.Authorization =
                        new AuthenticationHeaderValue("Bearer", newToken);

                    response = await _httpClient.GetAsync(apiUrl);
                    if (response.IsSuccessStatusCode)
                    {
                        var content = await response.Content.ReadAsStringAsync();
                        _logger.LogInformation("External API call successful after token refresh");
                        return content;
                    }
                }

                throw new UnauthorizedAccessException("API call failed after token refresh");
            }
            else
            {
                _logger.LogError("External API call failed with status: {StatusCode}", response.StatusCode);
                throw new HttpRequestException($"API call failed with status: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling external API");
            throw;
        }
    }

    /// <summary>
    /// Checks if we can make API calls (has valid tokens)
    /// </summary>
    public async Task<bool> CanMakeApiCallsAsync()
    {
        var hasValidToken = await _tokenService.HasValidTokenAsync();
        var refreshTokenExpired = await _tokenService.IsRefreshTokenExpiredAsync();

        return hasValidToken && !refreshTokenExpired;
    }

    /// <summary>
    /// Gets token status information
    /// </summary>
    public async Task<object> GetTokenStatusAsync()
    {
        var accessToken = await _tokenService.GetAccessTokenAsync();
        var hasValidToken = await _tokenService.HasValidTokenAsync();
        var refreshTokenExpired = await _tokenService.IsRefreshTokenExpiredAsync();

        return new
        {
            has_access_token = !string.IsNullOrEmpty(accessToken),
            access_token_valid = hasValidToken,
            refresh_token_expired = refreshTokenExpired,
            can_make_api_calls = hasValidToken && !refreshTokenExpired
        };
    }
}