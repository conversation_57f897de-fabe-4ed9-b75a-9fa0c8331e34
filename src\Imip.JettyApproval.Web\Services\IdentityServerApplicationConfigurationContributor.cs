using System;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Users;
using Imip.JettyApproval.Web.Services.Interfaces;
using Imip.JettyApproval.Web.Authorization.Permissions;

namespace Imip.JettyApproval.Web.Services;

/// <summary>
/// Custom ApplicationConfigurationContributor that fetches data from the identity server
/// instead of the local database. This contributor overrides the default ABP behavior.
/// </summary>
public class IdentityServerApplicationConfigurationContributor : IApplicationConfigurationContributor
{
    private readonly ILogger<IdentityServerApplicationConfigurationContributor> _logger;
    private readonly ITokenService _tokenService;
    private readonly ApplicationConfigurationService _applicationConfigurationService;
    private readonly ICurrentUser _currentUser;

    public IdentityServerApplicationConfigurationContributor(
        ILogger<IdentityServerApplicationConfigurationContributor> logger,
        ITokenService tokenService,
        ApplicationConfigurationService applicationConfigurationService,
        ICurrentUser currentUser)
    {
        _logger = logger;
        _tokenService = tokenService;
        _applicationConfigurationService = applicationConfigurationService;
        _currentUser = currentUser;

        // Log constructor call for debugging
        Console.WriteLine("=== IdentityServerApplicationConfigurationContributor CONSTRUCTOR CALLED ===");
        _logger.LogWarning("=== IdentityServerApplicationConfigurationContributor CONSTRUCTOR CALLED ===");
    }

    public async Task ContributeAsync(ApplicationConfigurationContributorContext context)
    {
        _logger.LogWarning("=== IdentityServerApplicationConfigurationContributor.ContributeAsync CALLED ===");

        try
        {
            // Check if user is authenticated
            if (!_currentUser.IsAuthenticated)
            {
                _logger.LogWarning("=== User is not authenticated, skipping Identity Server configuration ===");
                return;
            }

            // Get the access token
            var accessToken = await _tokenService.GetAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
                _logger.LogWarning("No access token found, skipping Identity Server configuration");
            {
                _logger.LogWarning("No access token found, skipping Identity Server configuration");
                return;
            }

            _logger.LogWarning($"=== Access token found (length: {accessToken.Length}), first 20 chars: {accessToken.Substring(0, Math.Min(20, accessToken.Length))}... ===");

            // Fetch complete configuration from Identity Server
            var identityServerConfig = await _applicationConfigurationService.GetCompleteApplicationConfigurationAsync(accessToken);
            if (identityServerConfig != null)
            {
                // Convert the object to ApplicationConfigurationDto
                var configJson = JsonSerializer.Serialize(identityServerConfig);

                // Log a portion of the raw response for debugging
                var jsonPreview = configJson.Length > 500 ? configJson.Substring(0, 500) + "..." : configJson;
                _logger.LogWarning("=== Raw Identity Server response preview: {JsonPreview} ===", jsonPreview);

                var identityServerAppConfig = JsonSerializer.Deserialize<ApplicationConfigurationDto>(configJson);

                if (identityServerAppConfig != null)
                {
                    // Override auth configuration with identity server data
                    if (identityServerAppConfig.Auth != null)
                    {
                        _logger.LogWarning("=== Overriding auth configuration with Identity Server data ===");
                        _logger.LogWarning("=== Granted policies count from Identity Server: {Count} ===",
                            identityServerAppConfig.Auth.GrantedPolicies?.Count ?? 0);

                        context.ApplicationConfiguration.Auth = identityServerAppConfig.Auth;
                    }

                    // Override current user with identity server data
                    if (identityServerAppConfig.CurrentUser != null)
                    {
                        _logger.LogDebug("Overriding current user configuration with Identity Server data");
                        _logger.LogDebug("Current user from Identity Server: {UserName}, Roles: {RoleCount}",
                            identityServerAppConfig.CurrentUser.UserName ?? "Unknown",
                            identityServerAppConfig.CurrentUser.Roles?.Length ?? 0);

                        context.ApplicationConfiguration.CurrentUser = identityServerAppConfig.CurrentUser;
                    }

                    // Override settings if available
                    if (identityServerAppConfig.Setting != null)
                    {
                        _logger.LogDebug("Overriding setting configuration with Identity Server data");
                        context.ApplicationConfiguration.Setting = identityServerAppConfig.Setting;
                    }

                    // Override features if available
                    if (identityServerAppConfig.Features != null)
                    {
                        _logger.LogDebug("Overriding features configuration with Identity Server data");
                        context.ApplicationConfiguration.Features = identityServerAppConfig.Features;
                    }

                    // Override global features if available
                    if (identityServerAppConfig.GlobalFeatures != null)
                    {
                        _logger.LogDebug("Overriding global features configuration with Identity Server data");
                        context.ApplicationConfiguration.GlobalFeatures = identityServerAppConfig.GlobalFeatures;
                    }

                    // Override localization if available
                    if (identityServerAppConfig.Localization != null)
                    {
                        _logger.LogDebug("Overriding localization configuration with Identity Server data");
                        context.ApplicationConfiguration.Localization = identityServerAppConfig.Localization;
                    }

                    // Override timing if available
                    if (identityServerAppConfig.Timing != null)
                    {
                        _logger.LogDebug("Overriding timing configuration with Identity Server data");
                        context.ApplicationConfiguration.Timing = identityServerAppConfig.Timing;
                    }

                    // Override clock if available
                    if (identityServerAppConfig.Clock != null)
                    {
                        _logger.LogDebug("Overriding clock configuration with Identity Server data");
                        context.ApplicationConfiguration.Clock = identityServerAppConfig.Clock;
                    }

                    // Override multi-tenancy if available
                    if (identityServerAppConfig.MultiTenancy != null)
                    {
                        _logger.LogDebug("Overriding multi-tenancy configuration with Identity Server data");
                        context.ApplicationConfiguration.MultiTenancy = identityServerAppConfig.MultiTenancy;
                    }

                    // Override current tenant if available
                    if (identityServerAppConfig.CurrentTenant != null)
                    {
                        _logger.LogDebug("Overriding current tenant configuration with Identity Server data");
                        context.ApplicationConfiguration.CurrentTenant = identityServerAppConfig.CurrentTenant;
                    }

                    // Override object extensions if available
                    if (identityServerAppConfig.ObjectExtensions != null)
                    {
                        _logger.LogDebug("Overriding object extensions configuration with Identity Server data");
                        context.ApplicationConfiguration.ObjectExtensions = identityServerAppConfig.ObjectExtensions;
                    }

                    _logger.LogInformation("Successfully overridden application configuration with Identity Server data");
                }
            }
            else
            {
                _logger.LogWarning("Failed to get configuration from Identity Server, using default configuration");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error contributing application configuration from Identity Server");
            // Don't throw - let the default configuration be used
        }
    }
}
