using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Users;
using Imip.JettyApproval.Web.Services.Interfaces;
using Imip.JettyApproval.Web.Authorization.Permissions;
using System.Collections.Generic;

namespace Imip.JettyApproval.Web.Services;

/// <summary>
/// Custom ApplicationConfigurationContributor that fetches data from the identity server
/// instead of the local database. This contributor overrides the default ABP behavior.
/// </summary>
public class IdentityServerApplicationConfigurationContributor : IApplicationConfigurationContributor
{
    private readonly ILogger<IdentityServerApplicationConfigurationContributor> _logger;
    private readonly ITokenService _tokenService;
    private readonly ApplicationConfigurationService _applicationConfigurationService;
    private readonly ICurrentUser _currentUser;
    private readonly IConfiguration _configuration;
    private readonly IHttpClientFactory _httpClientFactory;

    public IdentityServerApplicationConfigurationContributor(
        ILogger<IdentityServerApplicationConfigurationContributor> logger,
        ITokenService tokenService,
        ApplicationConfigurationService applicationConfigurationService,
        ICurrentUser currentUser,
        IConfiguration configuration,
        IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _tokenService = tokenService;
        _applicationConfigurationService = applicationConfigurationService;
        _currentUser = currentUser;
        _configuration = configuration;
        _httpClientFactory = httpClientFactory;

        // Log constructor call for debugging
        Console.WriteLine("=== IdentityServerApplicationConfigurationContributor CONSTRUCTOR CALLED ===");
        _logger.LogWarning("=== IdentityServerApplicationConfigurationContributor CONSTRUCTOR CALLED ===");
    }

    public async Task ContributeAsync(ApplicationConfigurationContributorContext context)
    {
        _logger.LogWarning("=== IdentityServerApplicationConfigurationContributor.ContributeAsync CALLED ===");

        try
        {
            // Check if user is authenticated
            if (!_currentUser.IsAuthenticated)
            {
                _logger.LogWarning("=== User is not authenticated, skipping Identity Server configuration ===");
                return;
            }

            // Get the access token
            var accessToken = await _tokenService.GetAccessTokenAsync();
            _logger.LogWarning("Access token {token}", accessToken);
            if (string.IsNullOrEmpty(accessToken))
            {
                _logger.LogWarning("No access token found, skipping Identity Server configuration");
                return;
            }

            _logger.LogWarning($"=== Access token found (length: {accessToken.Length}), first 20 chars: {accessToken.Substring(0, Math.Min(20, accessToken.Length))}... ===");

            // Fetch complete configuration from Identity Server
            var identityServerConfig = await _applicationConfigurationService.GetCompleteApplicationConfigurationAsync(accessToken);

            // Fetch granted policies separately from the permission endpoint
            var grantedPolicies = await GetAllGrantedPoliciesAsync(accessToken);
            _logger.LogWarning("=== Fetched {Count} granted policies from Identity Server ===", grantedPolicies?.Count ?? 0);

            if (identityServerConfig != null)
            {
                // Convert the object to ApplicationConfigurationDto
                var configJson = JsonSerializer.Serialize(identityServerConfig);

                // Log a portion of the raw response for debugging
                var jsonPreview = configJson.Length > 500 ? configJson.Substring(0, 500) + "..." : configJson;
                _logger.LogWarning("=== Raw Identity Server response preview: {JsonPreview} ===", jsonPreview);

                var identityServerAppConfig = JsonSerializer.Deserialize<ApplicationConfigurationDto>(configJson);

                if (identityServerAppConfig != null)
                {
                    // Override auth configuration with identity server data
                    if (identityServerAppConfig.Auth != null)
                    {
                        _logger.LogWarning("=== Overriding auth configuration with Identity Server data ===");
                        _logger.LogWarning("=== Granted policies count from Identity Server config: {Count} ===",
                            identityServerAppConfig.Auth.GrantedPolicies?.Count ?? 0);

                        // Use the separately fetched granted policies if available
                        if (grantedPolicies != null && grantedPolicies.Count > 0)
                        {
                            _logger.LogWarning("=== Using separately fetched granted policies: {Count} ===", grantedPolicies.Count);
                            identityServerAppConfig.Auth.GrantedPolicies = grantedPolicies;
                        }

                        context.ApplicationConfiguration.Auth = identityServerAppConfig.Auth;
                    }
                    else if (grantedPolicies != null && grantedPolicies.Count > 0)
                    {
                        // If no auth config from identity server but we have granted policies, create auth config
                        _logger.LogWarning("=== Creating auth configuration with fetched granted policies: {Count} ===", grantedPolicies.Count);
                        context.ApplicationConfiguration.Auth = new ApplicationAuthConfigurationDto
                        {
                            GrantedPolicies = grantedPolicies
                        };
                    }

                    // Override current user with identity server data
                    if (identityServerAppConfig.CurrentUser != null)
                    {
                        _logger.LogDebug("Overriding current user configuration with Identity Server data");
                        _logger.LogDebug("Current user from Identity Server: {UserName}, Roles: {RoleCount}",
                            identityServerAppConfig.CurrentUser.UserName ?? "Unknown",
                            identityServerAppConfig.CurrentUser.Roles?.Length ?? 0);

                        context.ApplicationConfiguration.CurrentUser = identityServerAppConfig.CurrentUser;
                    }

                    // Override settings if available
                    if (identityServerAppConfig.Setting != null)
                    {
                        _logger.LogDebug("Overriding setting configuration with Identity Server data");
                        context.ApplicationConfiguration.Setting = identityServerAppConfig.Setting;
                    }

                    // Override features if available
                    if (identityServerAppConfig.Features != null)
                    {
                        _logger.LogDebug("Overriding features configuration with Identity Server data");
                        context.ApplicationConfiguration.Features = identityServerAppConfig.Features;
                    }

                    // Override global features if available
                    if (identityServerAppConfig.GlobalFeatures != null)
                    {
                        _logger.LogDebug("Overriding global features configuration with Identity Server data");
                        context.ApplicationConfiguration.GlobalFeatures = identityServerAppConfig.GlobalFeatures;
                    }

                    // Override localization if available
                    if (identityServerAppConfig.Localization != null)
                    {
                        _logger.LogDebug("Overriding localization configuration with Identity Server data");
                        context.ApplicationConfiguration.Localization = identityServerAppConfig.Localization;
                    }

                    // Override timing if available
                    if (identityServerAppConfig.Timing != null)
                    {
                        _logger.LogDebug("Overriding timing configuration with Identity Server data");
                        context.ApplicationConfiguration.Timing = identityServerAppConfig.Timing;
                    }

                    // Override clock if available
                    if (identityServerAppConfig.Clock != null)
                    {
                        _logger.LogDebug("Overriding clock configuration with Identity Server data");
                        context.ApplicationConfiguration.Clock = identityServerAppConfig.Clock;
                    }

                    // Override multi-tenancy if available
                    if (identityServerAppConfig.MultiTenancy != null)
                    {
                        _logger.LogDebug("Overriding multi-tenancy configuration with Identity Server data");
                        context.ApplicationConfiguration.MultiTenancy = identityServerAppConfig.MultiTenancy;
                    }

                    // Override current tenant if available
                    if (identityServerAppConfig.CurrentTenant != null)
                    {
                        _logger.LogDebug("Overriding current tenant configuration with Identity Server data");
                        context.ApplicationConfiguration.CurrentTenant = identityServerAppConfig.CurrentTenant;
                    }

                    // Override object extensions if available
                    if (identityServerAppConfig.ObjectExtensions != null)
                    {
                        _logger.LogDebug("Overriding object extensions configuration with Identity Server data");
                        context.ApplicationConfiguration.ObjectExtensions = identityServerAppConfig.ObjectExtensions;
                    }

                    _logger.LogInformation("Successfully overridden application configuration with Identity Server data");
                }
            }
            else
            {
                _logger.LogWarning("Failed to get configuration from Identity Server, using default configuration");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error contributing application configuration from Identity Server");
            // Don't throw - let the default configuration be used
        }
    }

    private async Task<Dictionary<string, bool>?> GetAllGrantedPoliciesAsync(string accessToken)
    {
        try
        {
            var identityServerUrl = _configuration["AuthServer:Authority"];

            // Try multiple endpoints to get all granted policies
            var endpoints = new[]
            {
                $"{identityServerUrl}/api/permission-check/all-granted",
                $"{identityServerUrl}/api/permission-check/multiple?*",
                $"{identityServerUrl}/api/permission-check/all"
            };

            var client = _httpClientFactory.CreateClient("IdentityServer");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

            foreach (var endpoint in endpoints)
            {
                _logger.LogWarning("=== Trying to fetch all granted policies from: {Endpoint} ===", endpoint);

                try
                {
                    var response = await client.GetAsync(endpoint);

                    if (response.IsSuccessStatusCode)
                    {
                        var content = await response.Content.ReadAsStringAsync();
                        _logger.LogWarning("=== All granted policies response from {Endpoint}: {Content} ===",
                            endpoint, content.Length > 200 ? content.Substring(0, 200) + "..." : content);

                        var policies = JsonSerializer.Deserialize<Dictionary<string, bool>>(content);
                        if (policies != null && policies.Count > 0)
                        {
                            return policies;
                        }
                    }
                    else
                    {
                        _logger.LogWarning("=== Failed to fetch granted policies from {Endpoint}, status: {StatusCode} ===",
                            endpoint, response.StatusCode);
                    }
                }
                catch (Exception endpointEx)
                {
                    _logger.LogWarning(endpointEx, "=== Error trying endpoint {Endpoint} ===", endpoint);
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching granted policies from Identity Server");
            return null;
        }
    }
}
