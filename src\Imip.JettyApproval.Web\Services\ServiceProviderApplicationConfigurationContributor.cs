using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations;

namespace Imip.JettyApproval.Web.Services;

/// <summary>
/// A wrapper contributor that resolves the actual contributor from the service provider at runtime
/// </summary>
/// <typeparam name="T">The type of the actual contributor</typeparam>
public class ServiceProviderApplicationConfigurationContributor<T> : IApplicationConfigurationContributor
    where T : class, IApplicationConfigurationContributor
{
    public async Task ContributeAsync(ApplicationConfigurationContributorContext context)
    {
        // Resolve the actual contributor from the service provider at runtime
        var actualContributor = context.ServiceProvider.GetRequiredService<T>();
        await actualContributor.ContributeAsync(context);
    }
}
