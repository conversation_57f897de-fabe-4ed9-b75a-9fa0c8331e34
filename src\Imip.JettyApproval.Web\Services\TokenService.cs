using Imip.JettyApproval.Web.Services.Interfaces;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Volo.Abp.Identity;
using Volo.Abp.Users;
using Volo.Abp.Data;

namespace Imip.JettyApproval.Web.Services;

public class TokenService : ITokenService
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IdentityUserManager _userManager;
    private readonly ICurrentUser _currentUser;
    private readonly IConfiguration _configuration;
    private readonly ILogger<TokenService> _logger;
    private readonly HttpClient _httpClient;

    public TokenService(
        IHttpContextAccessor httpContextAccessor,
        IdentityUserManager userManager,
        ICurrentUser currentUser,
        IConfiguration configuration,
        ILogger<TokenService> logger,
        HttpClient httpClient)
    {
        _httpContextAccessor = httpContextAccessor;
        _userManager = userManager;
        _currentUser = currentUser;
        _configuration = configuration;
        _logger = logger;
        _httpClient = httpClient;
    }

    public async Task<string> GetAccessTokenAsync()
    {
        try
        {
            // First try to get from authentication properties
            var context = _httpContextAccessor.HttpContext;
            if (context != null)
            {
                var authenticateResult = await context.AuthenticateAsync("Cookies");
                if (authenticateResult.Succeeded && authenticateResult.Properties?.Items.ContainsKey(".Token.access_token") == true)
                {
                    return authenticateResult.Properties.Items[".Token.access_token"];
                }
            }

            // Fallback to GetTokenAsync method
            if (context != null)
            {
                var token = await context.GetTokenAsync("access_token");
                if (!string.IsNullOrEmpty(token))
                    return token;
            }

            // 2. Try to get from user extra properties
            var userId = _currentUser.Id;
            if (!userId.HasValue && context?.User?.Identity?.IsAuthenticated == true)
            {
                var userIdClaim = context.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
                if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var parsedUserId))
                {
                    userId = parsedUserId;
                }
            }
            if (userId.HasValue)
            {
                var user = await _userManager.FindByIdAsync(userId.Value.ToString());
                if (user != null)
                {
                    var token = user.GetProperty<string>("AccessToken");
                    if (!string.IsNullOrEmpty(token))
                        return token;
                }
            }

            // 3. Fallback: Try to get token from Authorization header
            if (context?.Request?.Headers.TryGetValue("Authorization", out var authHeader) == true)
            {
                var headerValue = authHeader.ToString();
                if (headerValue.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
                {
                    var token = headerValue.Substring("Bearer ".Length).Trim();
                    if (!string.IsNullOrEmpty(token))
                        return token;
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting access token");
            return null;
        }
    }

    public async Task<string> GetRefreshTokenAsync()
    {
        try
        {
            // First try to get from authentication properties
            var context = _httpContextAccessor.HttpContext;
            if (context != null)
            {
                var authenticateResult = await context.AuthenticateAsync("Cookies");
                if (authenticateResult.Succeeded && authenticateResult.Properties?.Items.ContainsKey(".Token.refresh_token") == true)
                {
                    return authenticateResult.Properties.Items[".Token.refresh_token"];
                }
            }

            // Fallback to user properties
            if (_currentUser.Id.HasValue)
            {
                var user = await _userManager.FindByIdAsync(_currentUser.Id.Value.ToString());
                if (user != null)
                {
                    return user.GetProperty<string>("RefreshToken");
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting refresh token");
            return null;
        }
    }

    public async Task<string> RefreshAccessTokenAsync()
    {
        try
        {
            var refreshToken = await GetRefreshTokenAsync();
            if (string.IsNullOrEmpty(refreshToken))
            {
                _logger.LogWarning("No refresh token available");
                return null;
            }

            var authority = _configuration["AuthServer:Authority"];
            var clientId = _configuration["AuthServer:ClientId"];
            var clientSecret = _configuration["AuthServer:ClientSecret"];

            var tokenRequest = new
            {
                grant_type = "refresh_token",
                refresh_token = refreshToken,
                client_id = clientId,
                client_secret = clientSecret
            };

            var json = JsonSerializer.Serialize(tokenRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"{authority}/connect/token", content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var tokenResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);

                if (tokenResponse.TryGetProperty("access_token", out var accessTokenElement))
                {
                    var newAccessToken = accessTokenElement.GetString();

                    // Update the token in user properties
                    if (_currentUser.Id.HasValue)
                    {
                        var currentUser = await _userManager.FindByIdAsync(_currentUser.Id.Value.ToString());
                        if (currentUser != null)
                        {
                            currentUser.SetProperty("AccessToken", newAccessToken);
                            await _userManager.UpdateAsync(currentUser);
                        }
                    }

                    _logger.LogInformation("Access token refreshed successfully");
                    return newAccessToken;
                }
            }
            else
            {
                _logger.LogError("Failed to refresh token: {StatusCode}", response.StatusCode);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing access token");
            return null;
        }
    }

    public async Task<bool> HasValidTokenAsync()
    {
        var accessToken = await GetAccessTokenAsync();
        if (string.IsNullOrEmpty(accessToken))
        {
            return false;
        }

        // Check if token is expired
        return !IsTokenExpired(accessToken);
    }

    /// <summary>
    /// Gets a valid access token, refreshing if necessary
    /// </summary>
    public async Task<string> GetValidAccessTokenAsync()
    {
        var accessToken = await GetAccessTokenAsync();

        if (string.IsNullOrEmpty(accessToken))
        {
            _logger.LogWarning("No access token available");
            return null;
        }

        // Check if token is expired
        if (IsTokenExpired(accessToken))
        {
            _logger.LogInformation("Access token is expired, attempting to refresh");
            accessToken = await RefreshAccessTokenAsync();

            if (string.IsNullOrEmpty(accessToken))
            {
                _logger.LogError("Failed to refresh access token");
                return null;
            }
        }

        return accessToken;
    }

    /// <summary>
    /// Checks if a JWT token is expired
    /// </summary>
    private bool IsTokenExpired(string token)
    {
        try
        {
            if (string.IsNullOrEmpty(token))
                return true;

            var handler = new JwtSecurityTokenHandler();
            var jwtToken = handler.ReadJwtToken(token);

            // Check if token has expired
            if (jwtToken.ValidTo < DateTime.UtcNow)
            {
                _logger.LogInformation("Token expired at: {Expiration}", jwtToken.ValidTo);
                return true;
            }

            // Add a buffer (e.g., 5 minutes) to refresh before actual expiration
            var bufferTime = TimeSpan.FromMinutes(5);
            if (jwtToken.ValidTo < DateTime.UtcNow.Add(bufferTime))
            {
                _logger.LogInformation("Token will expire soon at: {Expiration}, refreshing now", jwtToken.ValidTo);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking token expiration");
            return true; // Assume expired if we can't parse the token
        }
    }

    /// <summary>
    /// Checks if refresh token is expired
    /// </summary>
    public async Task<bool> IsRefreshTokenExpiredAsync()
    {
        var refreshToken = await GetRefreshTokenAsync();
        if (string.IsNullOrEmpty(refreshToken))
        {
            return true;
        }

        return IsTokenExpired(refreshToken);
    }
}