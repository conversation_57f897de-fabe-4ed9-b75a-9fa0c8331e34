using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Volo.Abp.Identity;
using Volo.Abp.Security.Claims;

namespace Imip.JettyApproval.Web.Services;

public interface ITokenValidationService
{
    Task<ClaimsPrincipal?> ValidateTokenAsync(string token);
    Task<bool> IsValidTokenAsync(string token);
}

public class TokenValidationService : ITokenValidationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<TokenValidationService> _logger;
    private readonly IdentityUserManager _userManager;
    private readonly JwtSecurityTokenHandler _tokenHandler;

    public TokenValidationService(
        IConfiguration configuration,
        ILogger<TokenValidationService> logger,
        IdentityUserManager userManager)
    {
        _configuration = configuration;
        _logger = logger;
        _userManager = userManager;
        _tokenHandler = new JwtSecurityTokenHandler();
    }

    public async Task<ClaimsPrincipal?> ValidateTokenAsync(string token)
    {
        try
        {
            _logger.LogInformation("Validating JWT token from other app");

            // Configure token validation parameters
            var tokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                ValidIssuer = _configuration["AuthServer:Authority"],
                ValidAudience = _configuration["AuthServer:ClientId"],
                ClockSkew = TimeSpan.FromMinutes(5)
            };

            // Validate the token
            var principal = _tokenHandler.ValidateToken(token, tokenValidationParameters, out var validatedToken);

            if (principal == null)
            {
                _logger.LogWarning("Token validation returned null principal");
                return null;
            }

            // Extract user information
            var email = principal.FindFirst("email")?.Value ??
                       principal.FindFirst(ClaimTypes.Email)?.Value;
            var sub = principal.FindFirst("sub")?.Value ??
                     principal.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(email))
            {
                _logger.LogWarning("No email claim found in token");
                return null;
            }

            // Find the user in our system
            var user = await _userManager.FindByEmailAsync(email);
            if (user == null)
            {
                _logger.LogWarning("User not found in local system: {Email}", email);
                return null;
            }

            // Create a new claims principal with ABP-specific claims
            var claims = new List<Claim>
            {
                new Claim(AbpClaimTypes.UserId, user.Id.ToString()),
                new Claim(AbpClaimTypes.UserName, user.UserName),
                new Claim(AbpClaimTypes.Email, user.Email)
            };

            // Add original claims from the token
            foreach (var claim in principal.Claims)
            {
                if (!claims.Any(c => c.Type == claim.Type && c.Value == claim.Value))
                {
                    claims.Add(claim);
                }
            }

            var identity = new ClaimsIdentity(claims, "Bearer");
            var newPrincipal = new ClaimsPrincipal(identity);

            _logger.LogInformation("Successfully validated token for user: {Email}", email);
            return newPrincipal;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating JWT token");
            return null;
        }
    }

    public async Task<bool> IsValidTokenAsync(string token)
    {
        var principal = await ValidateTokenAsync(token);
        return principal != null;
    }
}